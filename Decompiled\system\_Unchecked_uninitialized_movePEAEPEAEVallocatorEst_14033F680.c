/*
 * Function: ??$_Unchecked_uninitialized_move@PEAEPEAEV?$allocator@E@std@@@stdext@@YAPEAEPEAE00AEAV?$allocator@E@std@@@Z
 * Address: 0x14033F680
 */

char *__fastcall stdext::_Unchecked_uninitialized_move<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Undefined_move_tag v9; // [sp+31h] [bp-17h]@4
  char *_Firsta; // [sp+50h] [bp+8h]@1
  char *_Lasta; // [sp+58h] [bp+10h]@1
  char *__formal; // [sp+60h] [bp+18h]@1
  std::allocator<unsigned char> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Move_cat<unsigned char *>(&__formal);
  return std::_Uninit_move<unsigned char *,unsigned char *,std::allocator<unsigned char>,std::_Undefined_move_tag>(
           _Firsta,
           _Lasta,
           __formal,
           _Ala,
           v9,
           v8);
}
