#pragma once

#include "framework.h"
#include "ZoneServerStructs.h"

class ZONESERVERHOOK_API HookManager {
public:
    static HookManager& GetInstance();

    bool Initialize();
    void Shutdown();

    // Hook management
    bool InstallHook(DWORD targetAddress, void* hookFunction, void** originalFunction, const std::string& functionName);
    bool RemoveHook(const std::string& functionName);
    bool RemoveAllHooks();

    // Hook status
    bool IsHookActive(const std::string& functionName);
    std::vector<HookInfo> GetActiveHooks();

    // Zone server specific hooks
    bool InstallAuthenticationHooks();
    bool InstallMonsterHooks();
    bool InstallMapHooks();
    bool InstallNetworkHooks();

    // Hook functions (static for detours)
    static void __fastcall Hook_LoginBillingManager(void* thisPtr, void* userDB);
    static bool __fastcall Hook_HackShieldVerify(void* thisPtr, void* param);
    static void __fastcall Hook_CreateMonster(void* thisPtr);
    static void __fastcall Hook_DestroyMonster(void* thisPtr);
    static void __fastcall Hook_MonsterAIUpdate(void* thisPtr);
    static void __fastcall Hook_EnterMap(void* thisPtr, void* mapData);
    static void __fastcall Hook_ExitMap(void* thisPtr, void* mapData);
    static void __fastcall Hook_EnterWorldRequest(void* thisPtr, void* request);
    static void __fastcall Hook_ExitWorldRequest(void* thisPtr, void* request);
    static void __fastcall Hook_ChatMapRequest(void* thisPtr, void* request);

private:
    HookManager() = default;
    ~HookManager() = default;
    HookManager(const HookManager&) = delete;
    HookManager& operator=(const HookManager&) = delete;

    bool ValidateAddress(DWORD address);
    void LogHookOperation(const std::string& operation, const std::string& functionName, bool success);

    std::mutex m_mutex;
    std::map<std::string, HookInfo> m_hooks;
    bool m_initialized = false;

    // Original function pointers
    static LoginFunction m_originalLoginBillingManager;
    static AuthFunction m_originalHackShieldVerify;
    static MonsterFunction m_originalCreateMonster;
    static MonsterFunction m_originalDestroyMonster;
    static MonsterFunction m_originalMonsterAIUpdate;
    static MapFunction m_originalEnterMap;
    static MapFunction m_originalExitMap;
    static void* m_originalEnterWorldRequest;
    static void* m_originalExitWorldRequest;
    static void* m_originalChatMapRequest;
};