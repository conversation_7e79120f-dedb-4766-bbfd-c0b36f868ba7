/*
 * Function: ??$_Uninit_fill_n@PEAPEAVCRaceBuffInfoByHolyQuest@@_KPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@std@@YAXPEAPEAVCRaceBuffInfoByHolyQuest@@_KAEBQEAV1@AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403BBEC0
 */

void __fastcall std::_Uninit_fill_n<CRaceBuffInfoByHolyQuest * *,unsigned __int64,CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, unsigned __int64 _Count, CRaceBuffInfoByHolyQuest *const *_Val, std::allocator<CRaceBuffInfoByHolyQuest *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CRaceBuffInfoByHolyQuest **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<CRaceBuffInfoByHolyQuest * *,unsigned __int64,CRaceBuffInfoByHolyQuest *>(
    _Firsta,
    _Count,
    _Val);
}
