#pragma once

#include "framework.h"

enum class LogLevel {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3
};

class ZONESERVERHOOK_API Logger {
public:
    static Logger& GetInstance();

    void Initialize(const std::string& logFilePath);
    void Shutdown();

    void Log(LogLevel level, const std::string& message);
    void Log(LogLevel level, const char* format, ...);

    void SetLogLevel(LogLevel level);
    void SetConsoleOutput(bool enabled);
    void SetFileOutput(bool enabled);

private:
    Logger() = default;
    ~Logger() = default;
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    void WriteToFile(const std::string& message);
    void WriteToConsole(const std::string& message);
    std::string GetTimestamp();
    std::string GetLogLevelString(LogLevel level);

    std::mutex m_mutex;
    std::ofstream m_logFile;
    LogLevel m_logLevel = LogLevel::Info;
    bool m_consoleOutput = true;
    bool m_fileOutput = true;
    bool m_initialized = false;
};