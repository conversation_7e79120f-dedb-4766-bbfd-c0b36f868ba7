/*
 * Function: ??$_Uninit_copy@PEAURoomCharInfo@@PEAU1@V?$allocator@URoomCharInfo@@@std@@@std@@YAPEAURoomCharInfo@@PEAU1@00AEAV?$allocator@URoomCharInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402E94F0
 */

RoomCharInfo *__fastcall std::_Uninit_copy<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, RoomCharInfo *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  RoomCharInfo *v10; // [sp+20h] [bp-18h]@4
  __int64 v11; // [sp+28h] [bp-10h]@4
  RoomCharInfo *_Val; // [sp+40h] [bp+8h]@1
  RoomCharInfo *v13; // [sp+48h] [bp+10h]@1
  RoomCharInfo *_Ptr; // [sp+50h] [bp+18h]@1
  std::allocator<RoomCharInfo> *v15; // [sp+58h] [bp+20h]@1

  v15 = _Al;
  _Ptr = _Dest;
  v13 = _Last;
  _Val = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = -2i64;
  v10 = _Dest;
  while ( _Val != v13 )
  {
    std::allocator<RoomCharInfo>::construct(v15, _Ptr, _Val);
    ++_Ptr;
    ++_Val;
  }
  return _Ptr;
}
