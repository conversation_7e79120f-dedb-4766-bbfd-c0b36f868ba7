/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::catch$0
 * Address: 0x14039C470
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 4i64 )
    std::allocator<unsigned long>::destroy(*(std::allocator<unsigned long> **)(i + 120), *(unsigned int **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
