#pragma once

#include "framework.h"

namespace Utils {
    // Memory utilities
    ZONESERVERHOOK_API bool IsValidMemoryAddress(void* address);
    ZONESERVERHOOK_API bool IsExecutableMemory(void* address);
    ZONESERVERHOOK_API DWORD GetModuleBaseAddress(const std::string& moduleName);
    ZONESERVERHOOK_API DWORD GetFunctionAddress(const std::string& moduleName, const std::string& functionName);

    // String utilities
    ZONESERVERHOOK_API std::string WideStringToString(const std::wstring& wstr);
    ZONESERVERHOOK_API std::wstring StringToWideString(const std::string& str);
    ZONESERVERHOOK_API std::string ToLower(const std::string& str);
    ZONESERVERHOOK_API std::string ToUpper(const std::string& str);
    ZONESERVERHOOK_API std::vector<std::string> Split(const std::string& str, char delimiter);

    // File utilities
    ZONESERVERHOOK_API bool FileExists(const std::string& filePath);
    ZONESERVERHOOK_API bool DirectoryExists(const std::string& dirPath);
    ZONESERVERHOOK_API bool CreateDirectoryRecursive(const std::string& dirPath);
    ZONESERVERHOOK_API std::string GetExecutableDirectory();
    ZONESERVERHOOK_API std::string GetCurrentTimestamp();

    // Process utilities
    ZONESERVERHOOK_API DWORD GetCurrentProcessId();
    ZONESERVERHOOK_API std::string GetProcessName(DWORD processId);
    ZONESERVERHOOK_API bool IsProcessRunning(const std::string& processName);

    // Cryptography utilities
    ZONESERVERHOOK_API std::string CalculateMD5(const std::string& data);
    ZONESERVERHOOK_API std::string CalculateSHA256(const std::string& data);
    ZONESERVERHOOK_API std::string Base64Encode(const std::string& data);
    ZONESERVERHOOK_API std::string Base64Decode(const std::string& data);

    // Math utilities
    ZONESERVERHOOK_API float CalculateDistance2D(float x1, float y1, float x2, float y2);
    ZONESERVERHOOK_API float CalculateDistance3D(float x1, float y1, float z1, float x2, float y2, float z2);
    ZONESERVERHOOK_API bool IsPointInRadius(float x, float y, float centerX, float centerY, float radius);

    // Time utilities
    ZONESERVERHOOK_API DWORD GetTickCount64Safe();
    ZONESERVERHOOK_API std::string FormatTime(const std::chrono::system_clock::time_point& timePoint);
    ZONESERVERHOOK_API std::chrono::system_clock::time_point ParseTime(const std::string& timeString);
}