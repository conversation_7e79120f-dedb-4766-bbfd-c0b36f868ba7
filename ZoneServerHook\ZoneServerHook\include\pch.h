#pragma once

#include "framework.h"

// Forward declarations
class HookManager;
class ModuleManager;
class Logger;
class Config;
class SecurityModule;
class BugFixModule;
class EnhancementModule;

// Include all project headers
#include "Logger.h"
#include "Config.h"
#include "Utils.h"
#include "ZoneServerStructs.h"
#include "HookManager.h"
#include "ModuleManager.h"
#include "SecurityModule.h"
#include "BugFixModule.h"
#include "EnhancementModule.h"