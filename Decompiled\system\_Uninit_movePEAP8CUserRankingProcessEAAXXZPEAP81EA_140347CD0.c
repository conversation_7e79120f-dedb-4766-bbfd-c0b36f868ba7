/*
 * Function: ??$_Uninit_move@PEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@U_Undefined_move_tag@3@@std@@YAPEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZ00AEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140347CD0
 */

void (__cdecl **__fastcall std::_Uninit_move<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>,std::_Undefined_move_tag>(void (__cdecl **_First)(CUserRankingProcess *this), void (__cdecl **_Last)(CUserRankingProcess *this), void (__cdecl **_Dest)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6))(CUserRankingProcess *this)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  void (__cdecl **_Firsta)(CUserRankingProcess *); // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
