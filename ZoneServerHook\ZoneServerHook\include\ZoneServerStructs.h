#pragma once

#include "framework.h"

// Zone Server Memory Addresses (based on decompiled analysis)
namespace ZoneServerAddresses {
    // Authentication functions
    constexpr DWORD LOGIN_BILLING_MANAGER = 0x140079030;
    constexpr DWORD HACKSHIELD_VERIFY = 0x140417250;
    constexpr DWORD ACCOUNT_SERVER_LOGIN = 0x1401F8140;

    // Monster functions
    constexpr DWORD CREATE_MONSTER = 0x140141C50;
    constexpr DWORD DESTROY_MONSTER = 0x1401424F0;
    constexpr DWORD MONSTER_AI_UPDATE = 0x14014FA30;

    // Map functions
    constexpr DWORD ENTER_MAP = 0x140184D30;
    constexpr DWORD EXIT_MAP = 0x140184EC0;
    constexpr DWORD MAP_OPERATION = 0x140197970;

    // Network functions
    constexpr DWORD ENTER_WORLD_REQUEST = 0x1401D0D30;
    constexpr DWORD EXIT_WORLD_REQUEST = 0x1401C9D20;
    constexpr DWORD CHAT_MAP_REQUEST = 0x1401C58C0;
}

// Forward declarations for zone server classes
class CUserDB;
class CMapData;
class CMonster;
class CBillingManager;
class CHackShieldExSystem;

// Base structures from decompiled code
struct BASE_HACKSHEILD_PARAM {
    void* vfptr;
    // Additional members would be here
};

struct CAsyncLogInfo {
    void* vfptr;
    char* dirPath;
    char* fileName;
    char* typeName;
    DWORD count;
    // Additional members
};

struct MonsterSetInfoData {
    DWORD monsterCode;
    DWORD level;
    DWORD hp;
    DWORD mp;
    float moveSpeed;
    DWORD aggroDistance;
    DWORD respawnTime;
    // Additional monster properties
};

struct MapPosition {
    float x;
    float y;
    float z;
    float rotation;
};

struct PlayerInfo {
    DWORD playerId;
    char playerName[32];
    DWORD level;
    DWORD mapCode;
    MapPosition position;
    DWORD sessionId;
    bool isOnline;
    // Additional player data
};

// Hook information structure
struct HookInfo {
    DWORD originalAddress;
    void* originalFunction;
    void* hookFunction;
    std::string functionName;
    bool isActive;
};

// Security event structure
struct SecurityEvent {
    DWORD playerId;
    std::string eventType;
    std::string description;
    std::chrono::system_clock::time_point timestamp;
    DWORD severity; // 1=Low, 2=Medium, 3=High, 4=Critical
};

// ============================================================================
// DECOMPILED ZONE SERVER STRUCTURES
// Source: D:\4_GameGuardProject2232server\Decompiled\ZoneServerUD_x64.h
// ============================================================================

namespace ZoneServer {
    // Basic types from decompiled data
    using __int8 = char;
    using __int16 = short;
    using __int32 = int;
    using __int64 = long long;

    // Forward declarations from decompiled header
    struct CGameObjectVtbl;
    struct CPlayerDB;
    struct CCharacter;
    struct _base_fld;
    struct _object_id;

    // Core game object structure from decompiled data (line 8262)
    struct CGameObject {
        CGameObjectVtbl* vfptr;                 // Virtual function table pointer
        _base_fld* m_pRecordSet;               // Record set pointer
        _object_id m_ObjID;                    // Object ID
        DWORD m_dwObjSerial;                   // Object serial number
        bool m_bLive;                          // Is object alive
        int m_nTotalObjIndex;                  // Total object index
        bool m_bCorpse;                        // Is corpse
        bool m_bMove;                          // Is moving
        bool m_bStun;                          // Is stunned
        // Additional fields would follow in actual structure
    };

    // Virtual function table for CGameObject (line 9389)
    struct CGameObjectVtbl {
        void* (__cdecl* __vecDelDtor)(CGameObject* this_, unsigned int);
        char gap8[80];                         // Gap in vtable
        int (__cdecl* GetHP)(CGameObject* this_);
        bool (__cdecl* SetHP)(CGameObject* this_, int hp, bool flag);
        int (__cdecl* GetMaxHP)(CGameObject* this_);
        void (__cdecl* RecvKillMessage)(CGameObject* this_, CCharacter* killer);
        void (__cdecl* SFContInsertMessage)(CGameObject* this_, char, char, bool, void* player);
        void (__cdecl* SFContDelMessage)(CGameObject* this_, char, char, bool, bool);
        void (__cdecl* SFContUpdateTimeMessage)(CGameObject* this_, char, char, int);
        void (__cdecl* BeTargeted)(CGameObject* this_, CCharacter*);
        bool (__cdecl* RobbedHP)(CGameObject* this_, CCharacter*, int);
        bool (__cdecl* FixTargetWhile)(CGameObject* this_, CCharacter*, DWORD);
        void (__cdecl* SetAttackPart)(CGameObject* this_, int);
        int (__cdecl* GetGenAttackProb)(CGameObject* this_, CCharacter*, int, bool);
        int (__cdecl* SetDamage)(CGameObject* this_, int, CCharacter*, int, bool, int, DWORD, bool);
        int (__cdecl* GetDefFC)(CGameObject* this_, int, CCharacter*, int*);
        int (__cdecl* GetFireTol)(CGameObject* this_);
        int (__cdecl* GetWaterTol)(CGameObject* this_);
        int (__cdecl* GetSoilTol)(CGameObject* this_);
        int (__cdecl* GetWindTol)(CGameObject* this_);
        float (__cdecl* GetDefGap)(CGameObject* this_, int);
        float (__cdecl* GetDefFacing)(CGameObject* this_, int);
        // Additional virtual functions would follow
    };

    // Player structure from decompiled data (line 9190)
    // Note: CPlayer inherits from CCharacter which inherits from CGameObject
    struct CPlayer {
        // CGameObject base data would be here first
        CGameObjectVtbl* vfptr;                // Inherited from CGameObject

        // CPlayer specific data
        bool m_bLoad;                          // Is player loaded
        bool m_bOper;                          // Is operator
        bool m_bPostLoad;                      // Post load flag
        bool m_bFullMode;                      // Full mode flag
        char m_byUserDgr;                      // User degree
        char m_bySubDgr;                       // Sub degree
        bool m_bFirstStart;                    // First start flag
        bool m_bOutOfMap;                      // Out of map flag
        char m_byMoveDirect;                   // Movement direction
        char m_byPlusKey;                      // Plus key
        WORD m_wXorKey;                        // XOR key for encryption
        DWORD m_dwMoveCount;                   // Movement counter
        DWORD m_dwTargetCount;                 // Target counter
        DWORD m_dwAttackCount;                 // Attack counter
        // Many more fields from decompiled structure...

        // Position and movement data
        float m_fSendTarPos[2];                // Target position for sending

        // Map and location data
        char m_byPosRaceTown;                  // Position race town
        void* m_pBeforeTownCheckMap;           // Previous town check map
        float m_fBeforeTownCheckPos[2];        // Previous town check position

        // Combat and targeting
        struct {
            CGameObject* pObject;              // Target object
            char byKind;                       // Target kind
            char byID;                         // Target ID
            DWORD dwSerial;                    // Target serial
            WORD wHPRate;                      // HP rate
        } m_TargetObject;

        // Mining and resource gathering
        bool m_bMineMode;                      // Mining mode flag
        DWORD m_dwMineNextTime;                // Next mining time
        WORD m_wBatterySerialTmp;              // Battery serial temp
        char m_bySelectOreIndex;               // Selected ore index
        char m_byDelaySec;                     // Delay seconds
        short m_zMinePos[2];                   // Mining position

        // PvP and chaos mode
        int m_nChaosMode;                      // Chaos mode state
        DWORD m_dwChaosModeTime10Per;          // Chaos mode time 10%

        // Additional player data would follow...
    };

    // Map data structure from decompiled data
    struct CMapDataVtbl {
        void* (__cdecl* __vecDelDtor)(void* this_, unsigned int);
    };

    struct CMapData {
        CMapDataVtbl* vfptr;                   // Virtual function table
        // Additional fields from decompiled structure would follow
    };

    // Cheat command function type (line 73516)
    typedef bool (__cdecl* CheatCommandFn)(CPlayer* player);

    // Common enumerations from decompiled data
    namespace Enums {
        // Player chaos mode states (line 1930)
        enum CHAOS_MODE_STATE {
            chaos_mode_remain_time_0 = 0x0,
            chaos_mode_remain_time_10per_up = 0x1,
            chaos_mode_remain_time_10per_down = 0x2,
        };

        // PVP lose reasons (line 1957)
        enum PVP_LOSE {
            race_battle = 0x0,
            no_connect_long_time = 0x1,
        };

        // Point types (line 1938)
        enum POINT_TYPE {
            point_hp = 0x0,
            point_fp = 0x1,
            point_sp = 0x2,
            point_chaos = 0x3,
            POINT_NUM = 0x4,
        };
    }

    // Function addresses from decompiled analysis
    namespace Functions {
        // Player management functions
        constexpr uintptr_t PLAYER_SET_POSITION = 0x0;     // To be filled with actual address
        constexpr uintptr_t PLAYER_GET_HP = 0x0;           // CGameObject::GetHP
        constexpr uintptr_t PLAYER_SET_HP = 0x0;           // CGameObject::SetHP
        constexpr uintptr_t PLAYER_GET_MAX_HP = 0x0;       // CGameObject::GetMaxHP

        // Movement and teleportation functions
        constexpr uintptr_t VALIDATE_MOVEMENT = 0x0;       // Movement validation
        constexpr uintptr_t TELEPORT_PLAYER = 0x0;         // Player teleportation

        // Authentication functions (from /authentication/ folder)
        constexpr uintptr_t LOGIN_PLAYER = 0x0;            // Player login process
        constexpr uintptr_t LOGOUT_PLAYER = 0x0;           // Player logout process
        constexpr uintptr_t VALIDATE_SESSION = 0x0;        // Session validation

        // Security and anti-cheat functions
        constexpr uintptr_t SPEED_CHECK = 0x0;             // Speed hack detection
        constexpr uintptr_t POSITION_CHECK = 0x0;          // Position validation
        constexpr uintptr_t ITEM_VALIDATION = 0x0;         // Item duplication check

        // Map and world functions (from /world/ folder)
        constexpr uintptr_t MAP_CHANGE = 0x0;              // Map transition
        constexpr uintptr_t MONSTER_SPAWN = 0x0;           // Monster spawning
        constexpr uintptr_t WORLD_UPDATE = 0x0;            // World update loop
    }
}