#pragma once

#include "framework.h"

// Zone Server Memory Addresses (based on decompiled analysis)
namespace ZoneServerAddresses {
    // Authentication functions
    constexpr DWORD LOGIN_BILLING_MANAGER = 0x140079030;
    constexpr DWORD HACKSHIELD_VERIFY = 0x140417250;
    constexpr DWORD ACCOUNT_SERVER_LOGIN = 0x1401F8140;

    // Monster functions
    constexpr DWORD CREATE_MONSTER = 0x140141C50;
    constexpr DWORD DESTROY_MONSTER = 0x1401424F0;
    constexpr DWORD MONSTER_AI_UPDATE = 0x14014FA30;

    // Map functions
    constexpr DWORD ENTER_MAP = 0x140184D30;
    constexpr DWORD EXIT_MAP = 0x140184EC0;
    constexpr DWORD MAP_OPERATION = 0x140197970;

    // Network functions
    constexpr DWORD ENTER_WORLD_REQUEST = 0x1401D0D30;
    constexpr DWORD EXIT_WORLD_REQUEST = 0x1401C9D20;
    constexpr DWORD CHAT_MAP_REQUEST = 0x1401C58C0;
}

// Forward declarations for zone server classes
class CUserDB;
class CMapData;
class CMonster;
class CBillingManager;
class CHackShieldExSystem;

// Base structures from decompiled code
struct BASE_HACKSHEILD_PARAM {
    void* vfptr;
    // Additional members would be here
};

struct CAsyncLogInfo {
    void* vfptr;
    char* dirPath;
    char* fileName;
    char* typeName;
    DWORD count;
    // Additional members
};

struct MonsterSetInfoData {
    DWORD monsterCode;
    DWORD level;
    DWORD hp;
    DWORD mp;
    float moveSpeed;
    DWORD aggroDistance;
    DWORD respawnTime;
    // Additional monster properties
};

struct MapPosition {
    float x;
    float y;
    float z;
    float rotation;
};

struct PlayerInfo {
    DWORD playerId;
    char playerName[32];
    DWORD level;
    DWORD mapCode;
    MapPosition position;
    DWORD sessionId;
    bool isOnline;
    // Additional player data
};

// Hook information structure
struct HookInfo {
    DWORD originalAddress;
    void* originalFunction;
    void* hookFunction;
    std::string functionName;
    bool isActive;
};

// Security event structure
struct SecurityEvent {
    DWORD playerId;
    std::string eventType;
    std::string description;
    std::chrono::system_clock::time_point timestamp;
    DWORD severity; // 1=Low, 2=Medium, 3=High, 4=Critical
};