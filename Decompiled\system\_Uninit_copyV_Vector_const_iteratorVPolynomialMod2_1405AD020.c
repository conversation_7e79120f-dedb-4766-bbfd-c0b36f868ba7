/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@std@@PEAVPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@2@@std@@YAPEAVPolynomialMod2@CryptoPP@@V?$_Vector_const_iterator@VPolynomialMod2@CryptoPP@@V?$allocator@VPolynomialMod2@CryptoPP@@@std@@@0@0PEAV12@AEAV?$allocator@VPolynomialMod2@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405AD020
 */

__int64 __fastcall std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>,CryptoPP::PolynomialMod2 *,std::allocator<CryptoPP::PolynomialMod2>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 v6; // [sp+60h] [bp+8h]@1
  __int64 v7; // [sp+70h] [bp+18h]@1
  __int64 v8; // [sp+78h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a1;
  while ( std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>::operator!=() )
  {
    LODWORD(v4) = std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>::operator*(v6);
    std::allocator<CryptoPP::PolynomialMod2>::construct(v8, v7, v4);
    v7 += 24i64;
    std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>::operator++(v6);
  }
  std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>::~_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>();
  std::_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>::~_Vector_const_iterator<CryptoPP::PolynomialMod2,std::allocator<CryptoPP::PolynomialMod2>>();
  return v7;
}
