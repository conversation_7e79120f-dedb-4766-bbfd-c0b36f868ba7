/*
 * Function: ??$_Uninit_copy@PEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@std@@YAPEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZ00AEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140347F00
 */

void (__cdecl **__fastcall std::_Uninit_copy<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), void (__cdecl **_Last)(CUserRankingProcess *this), void (__cdecl **_Dest)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6))(CUserRankingProcess *this)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  void (__cdecl **v10)(CUserRankingProcess *); // [sp+20h] [bp-18h]@4
  __int64 v11; // [sp+28h] [bp-10h]@4
  void (__cdecl *const *_Val)(CUserRankingProcess *); // [sp+40h] [bp+8h]@1
  void (__cdecl **v13)(CUserRankingProcess *); // [sp+48h] [bp+10h]@1
  void (__cdecl **_Ptr)(CUserRankingProcess *); // [sp+50h] [bp+18h]@1
  std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *v15; // [sp+58h] [bp+20h]@1

  v15 = _Al;
  _Ptr = _Dest;
  v13 = _Last;
  _Val = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = -2i64;
  v10 = _Dest;
  while ( _Val != v13 )
  {
    std::allocator<void (CUserRankingProcess::*)(void)>::construct(v15, _Ptr, _Val);
    ++_Ptr;
    ++_Val;
  }
  return _Ptr;
}
