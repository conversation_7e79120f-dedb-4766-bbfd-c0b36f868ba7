/*
 * Function: _std::_Uninit_copy_CHEAT_COMMAND_____ptr64_CHEAT_COMMAND_____ptr64_std::allocator_CHEAT_COMMAND____::_1_::catch$0
 * Address: 0x140227A10
 */

void __fastcall __noreturn std::_Uninit_copy_CHEAT_COMMAND_____ptr64_CHEAT_COMMAND_____ptr64_std::allocator_CHEAT_COMMAND____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 32i64 )
    std::allocator<CHEAT_COMMAND>::destroy(*(std::allocator<CHEAT_COMMAND> **)(i + 88), *(CHEAT_COMMAND **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
