/*
 * Function: ??$_Uninit_move@PEAUEC2NPoint@CryptoPP@@PEAU12@V?$allocator@UEC2NPoint@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAUEC2NPoint@CryptoPP@@PEAU12@00AEAV?$allocator@UEC2NPoint@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7830
 */

int __fastcall std::_Uninit_move<CryptoPP::EC2NPoint *,CryptoPP::EC2NPoint *,std::allocator<CryptoPP::EC2NPoint>,std::_Undefined_move_tag>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::EC2NPoint *,CryptoPP::EC2NPoint *,std::allocator<CryptoPP::EC2NPoint>>(
           a1,
           a2,
           a3,
           a4);
}
