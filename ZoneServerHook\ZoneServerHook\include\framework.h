#pragma once

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>

// Detours library for API hooking
#include <detours.h>

// Project version
#define ZONESERVERHOOK_VERSION_MAJOR 1
#define ZONESERVERHOOK_VERSION_MINOR 0
#define ZONESERVERHOOK_VERSION_PATCH 0
#define ZONESERVERHOOK_VERSION_STRING "1.0.0"

// Export macros
#ifdef ZONESERVERHOOK_EXPORTS
#define ZONESERVERHOOK_API __declspec(dllexport)
#else
#define ZONESERVERHOOK_API __declspec(dllimport)
#endif

// Logging macros
#define LOG_INFO(msg) Logger::GetInstance().Log(LogLevel::Info, msg)
#define LOG_WARNING(msg) Logger::GetInstance().Log(LogLevel::Warning, msg)
#define LOG_ERROR(msg) Logger::GetInstance().Log(LogLevel::Error, msg)
#define LOG_DEBUG(msg) Logger::GetInstance().Log(LogLevel::Debug, msg)

// Common types
using BYTE = unsigned char;
using WORD = unsigned short;
using DWORD = unsigned long;
using QWORD = unsigned long long;

// Function pointer types for common zone server functions
typedef void(__fastcall* LoginFunction)(void* thisPtr, void* userDB);
typedef bool(__fastcall* AuthFunction)(void* thisPtr, int sessionId);
typedef void(__fastcall* MonsterFunction)(void* thisPtr);
typedef void(__fastcall* MapFunction)(void* thisPtr, void* mapData);

// Common constants
namespace Constants {
    constexpr size_t MAX_PLAYERS = 2000;
    constexpr size_t MAX_MONSTERS = 10000;
    constexpr size_t MAX_MAPS = 100;
    constexpr DWORD HOOK_TIMEOUT_MS = 5000;
}