/*
 * Function: ??$_Uninit_move@PEAUECPPoint@CryptoPP@@PEAU12@V?$allocator@UECPPoint@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAUECPPoint@CryptoPP@@PEAU12@00AEAV?$allocator@UECPPoint@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A78D0
 */

CryptoPP::ECPPoint *__fastcall std::_Uninit_move<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>,std::_Undefined_move_tag>(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2, CryptoPP::ECPPoint *a3, std::allocator<CryptoPP::ECPPoint> *a4)
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::ECPPoint *,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(
           a1,
           a2,
           a3,
           a4);
}
