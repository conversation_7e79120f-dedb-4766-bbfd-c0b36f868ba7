/*
 * Function: _RTC_UninitUse
 * Address: 0x1404DE0F0
 */

void __fastcall RTC_UninitUse(const char *varname)
{
  int v1; // er10@1
  const char *v2; // r8@1
  const char *v3; // r9@4
  __int64 v4; // rcx@4
  char v5; // al@5
  __int64 v6; // rdx@6
  char *v7; // rdi@6
  char v8; // al@7
  __int64 v9; // rcx@8
  char *v10; // rdi@8
  char v11; // al@9
  char v12; // [sp+1Fh] [bp-419h]@5
  char v13[1024]; // [sp+20h] [bp-418h]@4
  void *retaddr; // [sp+438h] [bp+0h]@12

  v1 = dword_1409784C4;
  v2 = varname;
  if ( dword_1409784C4 != -1 )
  {
    if ( varname && strlen(varname) + 58 <= 0x400 )
    {
      v3 = v13;
      v4 = 0i64;
      do
      {
        v5 = uninit_premsg[v4++];
        *(&v12 + v4) = v5;
      }
      while ( v5 );
      v7 = &v13[strlen(v13) + 1];
      v6 = 0i64;
      do
      {
        v8 = v2[v6++];
        v7[v6 - 2] = v8;
      }
      while ( v8 );
      v10 = &v13[strlen(v13) + 1];
      v9 = 0i64;
      do
      {
        v11 = uninit_postmsg[v9++];
        v10[v9 - 2] = v11;
      }
      while ( v11 );
    }
    else
    {
      v3 = "A variable is being used without being initialized.";
    }
    failwithmessage(retaddr, v1, 3, v3);
  }
}
