#pragma once

#include "framework.h"
#include "ZoneServerStructs.h"

class ZONESERVERHOOK_API BugFixModule {
public:
    BugFixModule();
    ~BugFixModule();

    bool Initialize();
    void Shutdown();

    // Bug fix implementations
    void FixMonsterAI(void* monsterPtr);
    void FixMapTransition(DWORD playerId, DWORD fromMap, DWORD toMap);
    void FixAuthenticationBug(void* userDBPtr);
    void FixMemoryLeak(void* objectPtr);
    void FixCrashBug(const std::string& bugType, void* contextPtr);

    // Event handlers
    void OnPlayerLogin(DWORD playerId, const std::string& playerName);
    void OnPlayerLogout(DWORD playerId);
    void OnMonsterSpawn(DWORD monsterId, DWORD monsterCode);
    void OnMonsterDeath(DWORD monsterId);
    void OnMapEnter(DWORD playerId, DWORD mapCode);
    void OnMapExit(DWORD playerId, DWORD mapCode);

    // Bug tracking
    void ReportBug(const std::string& bugType, const std::string& description);
    void MarkBugFixed(const std::string& bugType);
    bool IsBugFixed(const std::string& bugType);

private:
    void ApplyMonsterAIFix();
    void ApplyMapTransitionFix();
    void ApplyAuthenticationFix();
    void ApplyMemoryLeakFixes();
    void ApplyCrashFixes();

    std::mutex m_mutex;
    std::set<std::string> m_fixedBugs;
    std::map<std::string, DWORD> m_bugCounts;
    bool m_initialized = false;
};