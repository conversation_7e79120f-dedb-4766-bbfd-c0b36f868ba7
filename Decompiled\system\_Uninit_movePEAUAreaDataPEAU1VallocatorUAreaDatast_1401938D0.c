/*
 * Function: ??$_Uninit_move@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@U_Undefined_move_tag@3@@std@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1401938D0
 */

AreaData *__fastcall std::_Uninit_move<AreaData *,AreaData *,std::allocator<AreaData>,std::_Undefined_move_tag>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  AreaData *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<AreaData *,AreaData *,std::allocator<AreaData>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
