﻿  main.cpp
D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17): error C2678: binary '==': no operator found which takes a left-hand operand of type 'const std::string' (or there is no acceptable conversion)
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\guiddef.h(192,15):
      could be 'bool operator ==(const GUID &,const GUID &)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool operator ==(const GUID &,const GUID &)': cannot convert argument 1 from 'const std::string' to 'const GUID &'
              D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
              Reason: cannot convert from 'const std::string' to 'const GUID'
              D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
              No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
      D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
      or       'built-in C++ operator==(WCHAR [260], WCHAR [260])'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          '==': cannot convert argument 1 from 'const std::string' to 'WCHAR [260]'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(503,27):
      or       'bool std::operator ==(const std::pair<_Ty1,_Ty2> &,const std::pair<_Uty1,_Uty2> &)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::pair<_Ty1,_Ty2> &,const std::pair<_Uty1,_Uty2> &)': could not deduce template argument for 'const std::pair<_Ty1,_Ty2> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(1896,30):
      or       'bool std::operator ==(const std::reverse_iterator<_BidIt> &,const std::reverse_iterator<_BidIt2> &) noexcept(<expr>)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::reverse_iterator<_BidIt> &,const std::reverse_iterator<_BidIt2> &) noexcept(<expr>)': could not deduce template argument for 'const std::reverse_iterator<_BidIt> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4458,30):
      or       'bool std::operator ==(const std::move_iterator<_Iter> &,const std::move_iterator<_Iter2> &) noexcept(<expr>)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::move_iterator<_Iter> &,const std::move_iterator<_Iter2> &) noexcept(<expr>)': could not deduce template argument for 'const std::move_iterator<_Iter> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\iterator(304,17):
      or       'bool std::operator ==(const std::istream_iterator<_Ty,_Elem,_Traits,_Diff> &,const std::istream_iterator<_Ty,_Elem,_Traits,_Diff> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::istream_iterator<_Ty,_Elem,_Traits,_Diff> &,const std::istream_iterator<_Ty,_Elem,_Traits,_Diff> &) noexcept': could not deduce template argument for 'const std::istream_iterator<_Ty,_Elem,_Traits,_Diff> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\iterator(486,17):
      or       'bool std::operator ==(const std::istreambuf_iterator<_Elem,_Traits> &,const std::istreambuf_iterator<_Elem,_Traits> &)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::istreambuf_iterator<_Elem,_Traits> &,const std::istreambuf_iterator<_Elem,_Traits> &)': could not deduce template argument for 'const std::istreambuf_iterator<_Elem,_Traits> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory(1050,30):
      or       'bool std::operator ==(const std::allocator<_Ty> &,const std::allocator<_Other> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::allocator<_Ty> &,const std::allocator<_Other> &) noexcept': could not deduce template argument for 'const std::allocator<_Ty> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(3257,30):
      or       'bool std::operator ==(const std::basic_string<_Elem,_Traits,_Alloc> &,const std::basic_string<_Elem,_Traits,_Alloc> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::basic_string<_Elem,_Traits,_Alloc> &,const std::basic_string<_Elem,_Traits,_Alloc> &) noexcept': could not deduce template argument for 'const std::basic_string<_Elem,_Traits,_Alloc> &' from 'WCHAR [260]'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(3263,30):
      or       'bool std::operator ==(const std::basic_string<_Elem,_Traits,_Alloc> &,const _Elem *const ) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::basic_string<_Elem,_Traits,_Alloc> &,const _Elem *const ) noexcept': template parameter '_Elem' is ambiguous
              D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
              could be 'WCHAR'
              D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
              or       'char'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::basic_string<_Elem,_Traits,_Alloc> &,const _Elem *const ) noexcept': could not deduce template argument for 'const _Elem *const ' from 'WCHAR [260]'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(3282,17):
      or       'bool std::operator ==(const _Elem *const ,const std::basic_string<_Elem,_Traits,_Alloc> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const _Elem *const ,const std::basic_string<_Elem,_Traits,_Alloc> &) noexcept': could not deduce template argument for 'const _Elem *const ' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1880,17):
      or       'bool std::operator ==(const std::shared_ptr<_Ty> &,const std::shared_ptr<_Ty0> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::shared_ptr<_Ty> &,const std::shared_ptr<_Ty0> &) noexcept': could not deduce template argument for 'const std::shared_ptr<_Ty> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1917,17):
      or       'bool std::operator ==(const std::shared_ptr<_Ty> &,std::nullptr_t) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::shared_ptr<_Ty> &,std::nullptr_t) noexcept': could not deduce template argument for 'const std::shared_ptr<_Ty> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(1928,17):
      or       'bool std::operator ==(std::nullptr_t,const std::shared_ptr<_Ty> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(std::nullptr_t,const std::shared_ptr<_Ty> &) noexcept': could not deduce template argument for 'const std::shared_ptr<_Ty> &' from 'WCHAR [260]'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(3666,30):
      or       'bool std::operator ==(const std::unique_ptr<_Ty,_Dx> &,const std::unique_ptr<_Ty2,_Dx2> &)'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::unique_ptr<_Ty,_Dx> &,const std::unique_ptr<_Ty2,_Dx2> &)': could not deduce template argument for 'const std::unique_ptr<_Ty,_Dx> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(3712,30):
      or       'bool std::operator ==(const std::unique_ptr<_Ty,_Dx> &,std::nullptr_t) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(const std::unique_ptr<_Ty,_Dx> &,std::nullptr_t) noexcept': could not deduce template argument for 'const std::unique_ptr<_Ty,_Dx> &' from 'const std::string'
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(3718,17):
      or       'bool std::operator ==(std::nullptr_t,const std::unique_ptr<_Ty,_Dx> &) noexcept'
          D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
          'bool std::operator ==(std::nullptr_t,const std::unique_ptr<_Ty,_Dx> &) noexcept': could not deduce template argument for 'const std::unique_ptr<_Ty,_Dx> &' from 'WCHAR [260]'
      D:\4_GameGuardProject2232server\ZoneServerHook\Injector\main.cpp(99,17):
      while trying to match the argument list '(const std::string, WCHAR [260])'
  
