/*
 * Function: _std::_Uninit_copy_RoomCharInfo_____ptr64_RoomCharInfo_____ptr64_std::allocator_RoomCharInfo____::_1_::catch$0
 * Address: 0x1402E9580
 */

void __fastcall __noreturn std::_Uninit_copy_RoomCharInfo_____ptr64_RoomCharInfo_____ptr64_std::allocator_RoomCharInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 12i64 )
    std::allocator<RoomCharInfo>::destroy(*(std::allocator<RoomCharInfo> **)(i + 88), *(RoomCharInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
