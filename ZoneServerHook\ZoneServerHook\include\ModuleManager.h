#pragma once

#include "framework.h"

// Forward declarations
class SecurityModule;
class BugFixModule;
class EnhancementModule;

class ZONESERVERHOOK_API ModuleManager {
public:
    static ModuleManager& GetInstance();

    bool Initialize();
    void Shutdown();

    // Module management
    bool LoadModule(const std::string& moduleName);
    bool UnloadModule(const std::string& moduleName);
    bool IsModuleLoaded(const std::string& moduleName);

    // Module access
    SecurityModule* GetSecurityModule();
    BugFixModule* GetBugFixModule();
    EnhancementModule* GetEnhancementModule();

    // Module events
    void OnPlayerLogin(DWORD playerId, const std::string& playerName);
    void OnPlayerLogout(DWORD playerId);
    void OnMonsterSpawn(DWORD monsterId, DWORD monsterCode);
    void OnMonsterDeath(DWORD monsterId);
    void OnMapEnter(DWORD playerId, DWORD mapCode);
    void OnMapExit(DWORD playerId, DWORD mapCode);
    void OnChatMessage(DWORD playerId, const std::string& message);

private:
    ModuleManager() = default;
    ~ModuleManager() = default;
    ModuleManager(const ModuleManager&) = delete;
    ModuleManager& operator=(const ModuleManager&) = delete;

    std::mutex m_mutex;
    std::unique_ptr<SecurityModule> m_securityModule;
    std::unique_ptr<BugFixModule> m_bugFixModule;
    std::unique_ptr<EnhancementModule> m_enhancementModule;
    std::map<std::string, bool> m_loadedModules;
    bool m_initialized = false;
};