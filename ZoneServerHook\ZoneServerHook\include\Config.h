#pragma once

#include "framework.h"

class ZONESERVERHOOK_API Config {
public:
    static Config& GetInstance();

    bool Initialize(const std::string& configFilePath);
    void Shutdown();

    // Configuration getters
    std::string GetString(const std::string& section, const std::string& key, const std::string& defaultValue = "");
    int GetInt(const std::string& section, const std::string& key, int defaultValue = 0);
    bool GetBool(const std::string& section, const std::string& key, bool defaultValue = false);
    float GetFloat(const std::string& section, const std::string& key, float defaultValue = 0.0f);

    // Configuration setters
    void SetString(const std::string& section, const std::string& key, const std::string& value);
    void SetInt(const std::string& section, const std::string& key, int value);
    void SetBool(const std::string& section, const std::string& key, bool value);
    void SetFloat(const std::string& section, const std::string& key, float value);

    // Save configuration
    bool SaveConfig();
    bool ReloadConfig();

private:
    Config() = default;
    ~Config() = default;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

    bool LoadConfig();
    void CreateDefaultConfig();
    std::string GetConfigValue(const std::string& section, const std::string& key);
    void SetConfigValue(const std::string& section, const std::string& key, const std::string& value);

    std::mutex m_mutex;
    std::string m_configFilePath;
    std::map<std::string, std::map<std::string, std::string>> m_configData;
    bool m_initialized = false;
};