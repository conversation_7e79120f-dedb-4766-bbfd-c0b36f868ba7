/*
 * Function: ?_InitMineOre@AutoMineMachine@@AEAA_NXZ
 * Address: 0x1402D07E0
 */

char __fastcall AutoMineMachine::_InitMineOre(AutoMineMachine *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  unsigned __int8 v6; // [sp+24h] [bp-24h]@6
  _base_fld *v7; // [sp+28h] [bp-20h]@8
  CRecordData *v8; // [sp+30h] [bp-18h]@8
  CLogFile *v9; // [sp+38h] [bp-10h]@9
  AutoMineMachine *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 5; ++j )
  {
    v6 = GetItemTableCode(off_140970BC8[j]);
    if ( (signed int)v6 >= 37 )
    {
      CLogFile::Write(&v10->m_sysLog, "ERR::Invalid value of table code.(%d)", v6);
      return 0;
    }
    v8 = (CRecordData *)((char *)&unk_1799C6AA0 + 176 * v6);
    v7 = CRecordData::GetRecord(v8, off_140970BC8[j]);
    if ( !v7 )
    {
      v9 = &v10->m_sysLog;
      CLogFile::Write(&v10->m_sysLog, "ERR::Is not exist ore(%s)", off_140970BC8[j]);
      return 0;
    }
    v10->m_OreKind[j].bySlotIndex = 0;
    v10->m_OreKind[j].byTableCode = v6;
    v10->m_OreKind[j].wItemIndex = v7->m_dwIndex;
  }
  return 1;
}
