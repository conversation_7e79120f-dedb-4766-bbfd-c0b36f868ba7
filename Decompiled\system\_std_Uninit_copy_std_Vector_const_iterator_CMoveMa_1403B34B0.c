/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::catch$0
 * Address: 0x1403B34B0
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 8i64 )
    std::allocator<CMoveMapLimitRight *>::destroy(
      *(std::allocator<CMoveMapLimitRight *> **)(i + 120),
      *(CMoveMapLimitRight ***)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
