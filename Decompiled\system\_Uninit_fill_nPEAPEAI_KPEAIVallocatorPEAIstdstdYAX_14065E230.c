/*
 * Function: ??$_Uninit_fill_n@PEAPEAI_KPEAIV?$allocator@PEAI@std@@@std@@YAXPEAPEAI_KAEBQEAIAEAV?$allocator@PEAI@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14065E230
 */

int std::_Uninit_fill_n<unsigned int * *,unsigned __int64,unsigned int *,std::allocator<unsigned int *>>()
{
  return stdext::unchecked_fill_n<unsigned int * *,unsigned __int64,unsigned int *>();
}
