# ZoneServerHook - Decompiled Code Integration

## 📋 Overview

This document explains how the ZoneServerHook project integrates with the decompiled zone server files from IDA Pro analysis. The decompiled files serve as the foundation for understanding the target application's internal structure and implementing effective hooks.

## 🗂️ Decompiled Files Structure

### **Source Location**
```
D:\4_GameGuardProject2232server\Decompiled\
├── ZoneServerUD_x64.h          # Main header with all structures
├── authentication\             # Authentication and login functions
│   ├── LoginCBillingManager*.c
│   ├── OnCheckSession*.c
│   ├── ValidateSession*.c
│   └── ... (200+ authentication functions)
└── world\                      # World and game logic functions
    ├── CPlayer*.c
    ├── CGameObject*.c
    ├── CMapData*.c
    └── ... (500+ world functions)
```

### **Key Decompiled Components**

#### **1. Core Structures (ZoneServerUD_x64.h)**
- **CPlayer** (line 9190): Main player class with all player data
- **CGameObject** (line 8262): Base class for all game entities
- **CMapData**: Map and world information
- **Virtual Function Tables**: For hooking class methods

#### **2. Authentication Functions (/authentication/)**
- Login/logout processes
- Session validation
- Anti-cheat integration (HackShield)
- Billing system integration

#### **3. World Functions (/world/)**
- Player movement and positioning
- Monster AI and spawning
- Map operations and transitions
- Combat and interaction systems

## 🔧 Integration Strategy

### **1. Structure Mapping**
Our `ZoneServerStructs.h` maps decompiled structures to C++ classes:

```cpp
// From decompiled line 9190
struct CPlayer {
    CGameObjectVtbl* vfptr;        // Virtual function table
    bool m_bLoad;                  // Player loaded state
    bool m_bOper;                  // Operator privileges
    DWORD m_dwMoveCount;           // Movement counter (for speed detection)
    float m_fSendTarPos[2];        // Position data (for teleport detection)
    bool m_bMineMode;              // Mining state (for resource hack detection)
    // ... additional fields
};
```

### **2. Function Address Mapping**
We extract function addresses from decompiled analysis:

```cpp
namespace ZoneServerAddresses {
    // From authentication folder analysis
    constexpr DWORD LOGIN_BILLING_MANAGER = 0x140079030;
    constexpr DWORD HACKSHIELD_VERIFY = 0x140417250;
    
    // From world folder analysis  
    constexpr DWORD ENTER_MAP = 0x140184D30;
    constexpr DWORD MONSTER_AI_UPDATE = 0x14014FA30;
}
```

### **3. Hook Implementation**
Using Microsoft Detours to intercept key functions:

```cpp
// Hook player movement function
DetourTransactionBegin();
DetourUpdateThread(GetCurrentThread());
DetourAttach(&(PVOID&)OriginalMoveFunction, HookedMoveFunction);
DetourTransactionCommit();
```

## 🎯 Hook Targets

### **Security Hooks**
1. **Speed Hack Detection**
   - Hook: Player movement functions
   - Monitor: `m_dwMoveCount` and position changes
   - Source: `/world/` movement functions

2. **Teleport Hack Detection**
   - Hook: Position update functions
   - Monitor: Sudden position changes
   - Source: Map transition functions

3. **Item Duplication Detection**
   - Hook: Inventory manipulation functions
   - Monitor: Item quantity changes
   - Source: Item management functions

### **Enhancement Hooks**
1. **Bug Fixes**
   - Hook: Known buggy functions
   - Implement: Corrected logic
   - Source: Identified problematic functions

2. **Performance Improvements**
   - Hook: Resource-intensive functions
   - Implement: Optimized algorithms
   - Source: CPU-heavy operations

## 📊 Memory Layout Understanding

### **Player Object Layout**
```
CPlayer Object:
├── CGameObject (base)
│   ├── vfptr (0x00)
│   ├── m_pRecordSet (0x08)
│   ├── m_ObjID (0x10)
│   └── m_dwObjSerial (0x18)
├── CCharacter (inherited)
│   └── ... character data
└── CPlayer (specific)
    ├── m_bLoad (bool)
    ├── m_bOper (bool)
    ├── m_dwMoveCount (DWORD)
    └── ... player-specific data
```

### **Virtual Function Table Hooking**
```cpp
// Original VTable
CGameObjectVtbl* originalVTable = player->vfptr;

// Hook specific virtual functions
originalVTable->SetHP = HookedSetHP;
originalVTable->GetHP = HookedGetHP;
```

## 🔍 Analysis Workflow

### **1. Function Identification**
1. Use IDA Pro to analyze zone server executable
2. Export decompiled code to `/Decompiled/` folder
3. Identify key functions for hooking
4. Extract memory addresses and signatures

### **2. Structure Reverse Engineering**
1. Analyze class hierarchies in decompiled header
2. Map important data members
3. Understand virtual function tables
4. Document memory layouts

### **3. Hook Implementation**
1. Create hook functions matching original signatures
2. Implement security checks and enhancements
3. Use Detours to install hooks at runtime
4. Test and validate hook functionality

## 🛡️ Security Considerations

### **Anti-Detection Measures**
1. **Stealth Hooking**: Use advanced hooking techniques
2. **Code Obfuscation**: Protect hook logic from analysis
3. **Runtime Checks**: Validate hook integrity
4. **Memory Protection**: Secure hook data structures

### **Compatibility**
1. **Version Checking**: Validate target executable version
2. **Address Validation**: Verify function addresses before hooking
3. **Graceful Degradation**: Handle hook failures safely
4. **Update Mechanism**: Support for address updates

## 📈 Benefits of Decompiled Integration

### **Accuracy**
- **Precise Structures**: Exact memory layouts from decompilation
- **Correct Signatures**: Accurate function parameters and return types
- **Complete Coverage**: Access to all internal functions and data

### **Efficiency**
- **Targeted Hooks**: Hook only necessary functions
- **Minimal Overhead**: Efficient hook implementation
- **Optimized Detection**: Fast security checks

### **Maintainability**
- **Documented Code**: Clear mapping between hooks and original functions
- **Structured Approach**: Organized by functionality (auth, world, etc.)
- **Version Control**: Track changes in decompiled structures

## 🚀 Future Enhancements

### **Advanced Analysis**
1. **Dynamic Analysis**: Runtime behavior monitoring
2. **Pattern Recognition**: Automated cheat detection
3. **Machine Learning**: Adaptive security algorithms

### **Extended Coverage**
1. **Additional Modules**: Hook more game systems
2. **Client-Side Integration**: Coordinate with client hooks
3. **Database Integration**: Log security events to database

This integration approach ensures our ZoneServerHook project has deep understanding of the target application, enabling precise and effective hooking for security, bug fixes, and enhancements.
