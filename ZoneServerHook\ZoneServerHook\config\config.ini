# ZoneServerHook Configuration File
# Version 1.0.0

[General]
# Enable/disable the entire hook system
Enabled=true
# Log level: 0=Debug, 1=Info, 2=Warning, 3=Error
LogLevel=1
# Enable console output for debugging
ConsoleOutput=true
# Enable file logging
FileLogging=true

[Hooks]
# Enable authentication hooks (login, billing, hackshield)
EnableAuthentication=true
# Enable monster-related hooks (AI, spawning, etc.)
EnableMonster=true
# Enable map-related hooks (enter/exit, operations)
EnableMap=true
# Enable network hooks (requests, chat, etc.)
EnableNetwork=true

[Security]
# Enable security monitoring module
EnableSecurityModule=true
# Enable hack detection
EnableHackDetection=true
# Enable exploit detection
EnableExploitDetection=true
# Security event log file
SecurityLogFile=security_events.log
# Maximum security events to keep in memory
MaxSecurityEvents=1000

[BugFixes]
# Enable bug fix module
EnableBugFixModule=true
# Fix monster AI issues
FixMonsterAI=true
# Fix map transition bugs
FixMapTransitions=true
# Fix authentication issues
FixAuthenticationBugs=true

[Enhancements]
# Enable enhancement module
EnableEnhancementModule=true
# Enhanced logging for debugging
EnhancedLogging=true
# Performance monitoring
PerformanceMonitoring=true
# Memory usage tracking
MemoryTracking=false

[Monitoring]
# Monitor player connections
MonitorConnections=true
# Monitor monster spawning
MonitorMonsters=true
# Monitor map operations
MonitorMaps=true
# Monitor chat messages
MonitorChat=false

[Performance]
# Hook processing timeout in milliseconds
HookTimeout=5000
# Maximum number of concurrent hooks
MaxConcurrentHooks=50
# Enable performance profiling
EnableProfiling=false