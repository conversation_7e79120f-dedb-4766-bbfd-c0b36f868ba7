/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::dtor$1
 * Address: 0x14039BF20
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::~_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>(*(std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > **)(a2 + 176));
}
