/*
 * Function: _std::_Uninit_copy_AreaData_____ptr64_AreaData_____ptr64_std::allocator_AreaData____::_1_::catch$0
 * Address: 0x1401935F0
 */

void __fastcall __noreturn std::_Uninit_copy_AreaData_____ptr64_AreaData_____ptr64_std::allocator_AreaData____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 64i64 )
    std::allocator<AreaData>::destroy(*(std::allocator<AreaData> **)(i + 88), *(AreaData **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
