/*
 * Function: ??$_Uninit_move@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@PEAV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@@3@U_Undefined_move_tag@3@@std@@YAPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@0@PEAV120@00AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationCodeStr@@@std@@@2@@std@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1402116E0
 */

std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *__fastcall std::_Uninit_move<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,std::allocator<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>>,std::_Undefined_move_tag>(std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_First, std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_Last, std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_Dest, std::allocator<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> > *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0> *,std::allocator<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
