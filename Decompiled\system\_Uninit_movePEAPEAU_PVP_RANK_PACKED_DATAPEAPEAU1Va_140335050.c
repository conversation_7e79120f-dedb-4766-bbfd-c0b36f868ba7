/*
 * Function: ??$_Uninit_move@PEAPEAU_PVP_RANK_PACKED_DATA@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAU_PVP_RANK_PACKED_DATA@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140335050
 */

_PVP_RANK_PACKED_DATA **__fastcall std::_Uninit_move<_PVP_RANK_PACKED_DATA * *,_PVP_RANK_PACKED_DATA * *,std::allocator<_PVP_RANK_PACKED_DATA *>,std::_Undefined_move_tag>(_PVP_RANK_PACKED_DATA **_First, _PVP_RANK_PACKED_DATA **_Last, _PVP_RANK_PACKED_DATA **_Dest, std::allocator<_PVP_RANK_PACKED_DATA *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  _PVP_RANK_PACKED_DATA **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<_PVP_RANK_PACKED_DATA * *,_PVP_RANK_PACKED_DATA * *,std::allocator<_PVP_RANK_PACKED_DATA *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
