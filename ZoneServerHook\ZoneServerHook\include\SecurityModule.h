#pragma once

#include "framework.h"
#include "ZoneServerStructs.h"

class ZONESERVERHOOK_API SecurityModule {
public:
    SecurityModule();
    ~SecurityModule();

    bool Initialize();
    void Shutdown();

    // Security monitoring
    void OnPlayerLogin(DWORD playerId, const std::string& playerName);
    void OnPlayerLogout(DWORD playerId);
    void OnMonsterSpawn(DWORD monsterId, DWORD monsterCode);
    void OnMonsterDeath(DWORD monsterId);
    void OnMapEnter(DWORD playerId, DWORD mapCode);
    void OnMapExit(DWORD playerId, DWORD mapCode);
    void OnChatMessage(DWORD playerId, const std::string& message);

    // Hack detection
    bool DetectSpeedHack(DWORD playerId, const MapPosition& oldPos, const MapPosition& newPos, float deltaTime);
    bool DetectTeleportHack(DWORD playerId, const MapPosition& oldPos, const MapPosition& newPos);
    bool DetectDupeHack(DWORD playerId, DWORD itemId, DWORD quantity);
    bool DetectStatHack(DWORD playerId, DWORD statType, DWORD oldValue, DWORD newValue);
    bool DetectMonsterHack(DWORD playerId, DWORD monsterId, const MapPosition& monsterPos);

    // Exploit detection
    bool DetectMapExploit(DWORD playerId, DWORD mapCode);
    bool DetectItemExploit(DWORD playerId, DWORD itemId);
    bool DetectGoldExploit(DWORD playerId, DWORD goldAmount);
    bool DetectExpExploit(DWORD playerId, DWORD expAmount);

    // Security events
    void ReportSecurityEvent(const SecurityEvent& event);
    std::vector<SecurityEvent> GetRecentEvents(DWORD count = 100);
    void ClearSecurityEvents();

    // Player monitoring
    void AddSuspiciousPlayer(DWORD playerId, const std::string& reason);
    void RemoveSuspiciousPlayer(DWORD playerId);
    bool IsSuspiciousPlayer(DWORD playerId);

private:
    void ProcessSecurityEvent(const SecurityEvent& event);
    void LogSecurityEvent(const SecurityEvent& event);
    bool IsValidPosition(const MapPosition& pos, DWORD mapCode);
    float CalculateDistance(const MapPosition& pos1, const MapPosition& pos2);

    std::mutex m_mutex;
    std::vector<SecurityEvent> m_securityEvents;
    std::map<DWORD, PlayerInfo> m_playerInfo;
    std::set<DWORD> m_suspiciousPlayers;
    std::ofstream m_securityLogFile;
    bool m_initialized = false;

    // Security thresholds
    float m_maxMoveSpeed = 10.0f;
    float m_maxTeleportDistance = 50.0f;
    DWORD m_maxItemQuantity = 999999;
    DWORD m_maxStatIncrease = 1000;
    DWORD m_maxGoldAmount = 2000000000;
    DWORD m_maxExpAmount = 1000000;
};