/*
 * Function: ??$_Uninit_move@PEAGPEAGV?$allocator@G@std@@U_Undefined_move_tag@2@@std@@YAPEAGPEAG00AEAV?$allocator@G@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1406507A0
 */

int std::_Uninit_move<unsigned short *,unsigned short *,std::allocator<unsigned short>,std::_Undefined_move_tag>()
{
  return stdext::unchecked_uninitialized_copy<unsigned short *,unsigned short *,std::allocator<unsigned short>>();
}
