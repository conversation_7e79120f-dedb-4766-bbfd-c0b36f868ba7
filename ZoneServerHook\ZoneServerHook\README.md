# ZoneServerHook - Game Server Enhancement DLL

## Overview
ZoneServerHook is a comprehensive DLL injection framework designed to enhance, monitor, and secure MMORPG zone servers. It provides bug fixes, hack detection, exploit prevention, and performance enhancements through a modular hooking system.

## Features

### 🔒 Security Module
- **Hack Detection**: Speed hacks, teleport hacks, stat manipulation, item duplication
- **Exploit Prevention**: Map exploits, item exploits, gold/experience exploits
- **Player Monitoring**: Suspicious activity tracking and reporting
- **Real-time Logging**: Comprehensive security event logging

### 🐛 Bug Fix Module
- **Monster AI Fixes**: Resolves AI pathfinding and behavior issues
- **Map Transition Fixes**: Prevents crashes during map changes
- **Authentication Fixes**: Resolves login and session management bugs
- **Memory Leak Prevention**: Automatic cleanup of leaked resources

### ⚡ Enhancement Module
- **Performance Optimization**: Monster AI, map loading, network traffic
- **Enhanced Logging**: Detailed debugging and monitoring capabilities
- **Error Handling**: Improved crash prevention and recovery
- **Performance Monitoring**: Real-time system performance tracking

## Architecture

### Core Components
- **HookManager**: Manages API hooking using Microsoft Detours
- **ModuleManager**: Coordinates all enhancement modules
- **Logger**: Thread-safe logging system with multiple output targets
- **Config**: INI-based configuration management

### Hook Points
Based on decompiled zone server analysis, the system hooks:
- Authentication functions (login, billing, HackShield)
- Monster management (spawn, AI, death)
- Map operations (enter, exit, transitions)
- Network requests (world entry, chat, etc.)

## Project Structure
```
ZoneServerHook/
├── ZoneServerHook/          # Main DLL project
│   ├── include/             # Header files
│   │   ├── framework.h      # Core framework definitions
│   │   ├── HookManager.h    # API hooking management
│   │   ├── ModuleManager.h  # Module coordination
│   │   ├── SecurityModule.h # Security and hack detection
│   │   ├── BugFixModule.h   # Bug fixes and patches
│   │   ├── EnhancementModule.h # Performance enhancements
│   │   ├── Logger.h         # Logging system
│   │   ├── Config.h         # Configuration management
│   │   ├── Utils.h          # Utility functions
│   │   └── ZoneServerStructs.h # Zone server data structures
│   ├── src/                 # Source files
│   │   ├── dllmain.cpp      # DLL entry point
│   │   └── pch.cpp          # Precompiled header
│   ├── config/              # Configuration files
│   │   └── config.ini       # Main configuration
│   └── third_party/         # External libraries
│       └── detours/         # Microsoft Detours library
├── Injector/                # DLL injection utility
└── TestClient/              # Testing and validation tools
```

## Configuration

The system is configured via `config/config.ini`:

### Key Settings
- **Hooks**: Enable/disable specific hook categories
- **Security**: Configure detection thresholds and monitoring
- **BugFixes**: Enable specific bug fix modules
- **Enhancements**: Performance optimization settings
- **Logging**: Output levels and destinations

## Memory Addresses

Based on decompiled analysis, key function addresses:
- Login/Billing: `0x140079030`
- HackShield Verify: `0x140417250`
- Monster Creation: `0x140141C50`
- Map Operations: `0x140184D30`
- Network Requests: `0x1401D0D30`

*Note: These addresses may need adjustment based on the specific zone server version.*

## Building

### Prerequisites
- Visual Studio 2019/2022 with C++ support
- Windows 10 SDK
- Microsoft Detours library

### Build Steps
1. Open `ZoneServerHook.sln` in Visual Studio
2. Ensure Detours library is properly referenced
3. Build in Release mode for production use
4. Output DLL will be in `bin/Release/`

## Usage

### DLL Injection
1. Use the included Injector tool or any DLL injection method
2. Target the zone server process
3. Monitor logs for initialization status

### Configuration
1. Edit `config/config.ini` to enable desired features
2. Restart the zone server or reload the DLL
3. Monitor security logs for detected events

## Security Events

The system logs various security events:
- **Speed Hacks**: Movement speed exceeding thresholds
- **Teleport Hacks**: Instant position changes
- **Item Duplication**: Suspicious item quantity changes
- **Stat Manipulation**: Abnormal stat increases
- **Map Exploits**: Invalid map access attempts

## Development

### Adding New Hooks
1. Define function signature in `ZoneServerStructs.h`
2. Add hook installation in `HookManager.cpp`
3. Implement hook function with original call
4. Add configuration option in `config.ini`

### Adding New Modules
1. Create module header in `include/`
2. Implement module class with Initialize/Shutdown
3. Register module in `ModuleManager`
4. Add event handlers as needed

## Testing

Use the TestClient project to:
- Validate hook installation
- Test security detection
- Verify bug fixes
- Monitor performance impact

## Logging

Logs are written to:
- `ZoneServerHook.log` - General operation log
- `security_events.log` - Security-specific events
- Console output (if enabled)

## License

This project is for educational and security research purposes. Ensure compliance with applicable laws and terms of service.

## Contributing

1. Follow the existing code style
2. Add comprehensive logging
3. Update configuration options
4. Test thoroughly before submission

## Support

For issues or questions:
1. Check the logs for error messages
2. Verify configuration settings
3. Ensure proper DLL injection
4. Review memory addresses for version compatibility