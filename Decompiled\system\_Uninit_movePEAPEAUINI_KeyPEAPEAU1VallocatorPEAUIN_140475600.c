/*
 * Function: ??$_Uninit_move@PEAPEAUINI_Key@@PEAPEAU1@V?$allocator@PEAUI<PERSON>_Key@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAUINI_Key@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Key@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140475600
 */

INI_Key **__fastcall std::_Uninit_move<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>,std::_Undefined_move_tag>(INI_Key **_First, INI_Key **_Last, INI_Key **_Dest, std::allocator<INI_Key *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  INI_Key **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<INI_Key * *,INI_Key * *,std::allocator<INI_Key *>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
