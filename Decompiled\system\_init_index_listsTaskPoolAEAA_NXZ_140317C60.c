/*
 * Function: ?_init_index_lists@TaskPool@@AEAA_NXZ
 * Address: 0x140317C60
 */

char __fastcall TaskPool::_init_index_lists(TaskPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int dwIndex; // [sp+20h] [bp-18h]@10
  TaskPool *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNetIndexList::SetList(&v6->_listEmptyIdx, v6->_nMaxTskNum) )
  {
    if ( CNetIndexList::SetList(&v6->_listRegedIdx, v6->_nMaxTskNum) )
    {
      if ( CNetIndexList::SetList(&v6->_listCompleteIdx, v6->_nMaxTskNum) )
      {
        for ( dwIndex = 0; dwIndex < v6->_nMaxTskNum; ++dwIndex )
        {
          if ( !CNetIndexList::PushNode_Back(&v6->_listEmptyIdx, dwIndex) )
            return 0;
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
