/*
 * Function: ??$_Uninit_fill_n@PEAPEAUINI_Key@@_KPEAU1@V?$allocator@PEAUINI_Key@@@std@@@std@@YAXPEAPEAUINI_Key@@_KAEBQEAU1@AEAV?$allocator@PEAUI<PERSON>_Key@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140474FC0
 */

void __fastcall std::_Uninit_fill_n<INI_Key * *,unsigned __int64,INI_Key *,std::allocator<INI_Key *>>(INI_Key **_First, unsigned __int64 _Count, INI_Key *const *_Val, std::allocator<INI_Key *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  INI_Key **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<INI_Key * *,unsigned __int64,INI_Key *>(_Firsta, _Count, _Val);
}
