/*
 * Function: _std::_Uninit_copy_std::_Vector_iterator_std::pair_unsigned_long_unsigned_long__std::allocator_std::pair_unsigned_long_unsigned_long______std::pair_unsigned_long_unsigned_long______ptr64_std::allocator_std::pair_unsigned_long_unsigned_long______::_1_::dtor$1
 * Address: 0x14038D340
 */

void __fastcall std::_Uninit_copy_std::_Vector_iterator_std::pair_unsigned_long_unsigned_long__std::allocator_std::pair_unsigned_long_unsigned_long______std::pair_unsigned_long_unsigned_long______ptr64_std::allocator_std::pair_unsigned_long_unsigned_long______::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::~_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(*(std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > **)(a2 + 96));
}
