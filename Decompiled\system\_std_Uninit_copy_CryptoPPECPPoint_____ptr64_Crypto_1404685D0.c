/*
 * Function: _std::_Uninit_copy_CryptoPP::ECPPoint_____ptr64_CryptoPP::ECPPoint_____ptr64_std::allocator_CryptoPP::ECPPoint____::_1_::catch$0
 * Address: 0x1404685D0
 */

void __fastcall __noreturn std::_Uninit_copy_CryptoPP::ECPPoint_____ptr64_CryptoPP::ECPPoint_____ptr64_std::allocator_CryptoPP::ECPPoint____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 88i64 )
    std::allocator<CryptoPP::ECPPoint>::destroy(
      *(std::allocator<CryptoPP::ECPPoint> **)(i + 88),
      *(CryptoPP::ECPPoint **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
