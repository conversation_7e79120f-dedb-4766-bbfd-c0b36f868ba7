#include "pch.h"

// Global instances
static bool g_initialized = false;
static HMODULE g_hModule = nullptr;

// Forward declarations
DWORD WINAPI InitializeHookThread(LPVOID lpParam);
void CleanupHook();

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        g_hModule = hModule;

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications
        DisableThreadLibraryCalls(hModule);

        // Create initialization thread to avoid blocking DllMain
        HANDLE hThread = CreateThread(nullptr, 0, InitializeHookThread, nullptr, 0, nullptr);
        if (hThread) {
            CloseHandle(hThread);
        }
        break;
    }
    case DLL_PROCESS_DETACH:
        if (!lpReserved) { // Only cleanup if not process termination
            CleanupHook();
        }
        break;
    }
    return TRUE;
}

DWORD WINAPI InitializeHookThread(LPVOID lpParam)
{
    try {
        // Initialize logger first
        Logger::GetInstance().Initialize("ZoneServerHook.log");
        LOG_INFO("ZoneServerHook v" ZONESERVERHOOK_VERSION_STRING " - Initializing...");

        // Initialize configuration
        Config::GetInstance().Initialize("config\\config.ini");

        // Initialize hook manager
        if (!HookManager::GetInstance().Initialize()) {
            LOG_ERROR("Failed to initialize HookManager");
            return 1;
        }

        // Initialize module manager
        if (!ModuleManager::GetInstance().Initialize()) {
            LOG_ERROR("Failed to initialize ModuleManager");
            return 1;
        }

        // Install hooks based on configuration
        if (Config::GetInstance().GetBool("Hooks", "EnableAuthentication", true)) {
            HookManager::GetInstance().InstallAuthenticationHooks();
        }

        if (Config::GetInstance().GetBool("Hooks", "EnableMonster", true)) {
            HookManager::GetInstance().InstallMonsterHooks();
        }

        if (Config::GetInstance().GetBool("Hooks", "EnableMap", true)) {
            HookManager::GetInstance().InstallMapHooks();
        }

        if (Config::GetInstance().GetBool("Hooks", "EnableNetwork", true)) {
            HookManager::GetInstance().InstallNetworkHooks();
        }

        g_initialized = true;
        LOG_INFO("ZoneServerHook initialization completed successfully");

    } catch (const std::exception& e) {
        LOG_ERROR("Exception during initialization: " + std::string(e.what()));
        return 1;
    } catch (...) {
        LOG_ERROR("Unknown exception during initialization");
        return 1;
    }

    return 0;
}

void CleanupHook()
{
    if (!g_initialized) return;

    try {
        LOG_INFO("ZoneServerHook - Shutting down...");

        // Shutdown in reverse order
        ModuleManager::GetInstance().Shutdown();
        HookManager::GetInstance().Shutdown();
        Config::GetInstance().Shutdown();

        LOG_INFO("ZoneServerHook shutdown completed");
        Logger::GetInstance().Shutdown();

        g_initialized = false;

    } catch (...) {
        // Ignore exceptions during shutdown
    }
}