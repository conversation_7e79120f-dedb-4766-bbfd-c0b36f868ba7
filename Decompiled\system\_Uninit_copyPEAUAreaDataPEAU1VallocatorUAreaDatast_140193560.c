/*
 * Function: ??$_Uninit_copy@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@@std@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140193560
 */

AreaData *__fastcall std::_Uninit_copy<AreaData *,AreaData *,std::allocator<AreaData>>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  AreaData *v10; // [sp+20h] [bp-18h]@4
  __int64 v11; // [sp+28h] [bp-10h]@4
  AreaData *_Val; // [sp+40h] [bp+8h]@1
  AreaData *v13; // [sp+48h] [bp+10h]@1
  AreaData *_Ptr; // [sp+50h] [bp+18h]@1
  std::allocator<AreaData> *v15; // [sp+58h] [bp+20h]@1

  v15 = _Al;
  _Ptr = _Dest;
  v13 = _Last;
  _Val = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = -2i64;
  v10 = _Dest;
  while ( _Val != v13 )
  {
    std::allocator<AreaData>::construct(v15, _Ptr, _Val);
    ++_Ptr;
    ++_Val;
  }
  return _Ptr;
}
