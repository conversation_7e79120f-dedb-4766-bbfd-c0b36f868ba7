/*
 * Function: ??$_Unchecked_uninitialized_move@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@@stdext@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@std@@@Z
 * Address: 0x1401931C0
 */

AreaData *__fastcall stdext::_Unchecked_uninitialized_move<AreaData *,AreaData *,std::allocator<AreaData>>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Undefined_move_tag v9; // [sp+31h] [bp-17h]@4
  AreaData *_Firsta; // [sp+50h] [bp+8h]@1
  AreaData *_Lasta; // [sp+58h] [bp+10h]@1
  AreaData *__formal; // [sp+60h] [bp+18h]@1
  std::allocator<AreaData> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Move_cat<AreaData *>(&__formal);
  return std::_Uninit_move<AreaData *,AreaData *,std::allocator<AreaData>,std::_Undefined_move_tag>(
           _Firsta,
           _Lasta,
           __formal,
           _Ala,
           v9,
           v8);
}
