/*
 * Function: ?_InitSDM@CMonster@@SAXXZ
 * Address: 0x1401491A0
 */

void CMonster::_InitSDM(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v2; // eax@5
  unsigned int v3; // eax@7
  __int64 v4; // [sp+0h] [bp-E8h]@1
  char Dest; // [sp+40h] [bp-A8h]@5
  unsigned __int64 v6; // [sp+D0h] [bp-18h]@4

  v0 = &v4;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( !CMonster::s_logTrace_Boss_Looting.m_bInit )
  {
    v2 = GetKorLocalTime();
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\MonsterLoot%d.log", v2);
    CLogFile::SetWriteLogFile(&CMonster::s_logTrace_Boss_Looting, &Dest, 1, 0, 0, 0);
  }
  if ( !CMonster::s_logTrace_Boss_BirthAndDeath.m_bInit )
  {
    v3 = GetKorLocalTime();
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\MonsterBirth%d.log", v3);
    CLogFile::SetWriteLogFile(&CMonster::s_logTrace_Boss_BirthAndDeath, &Dest, 1, 0, 1, 1);
  }
  if ( !sPlayerDum.m_bLive )
  {
    sPlayerDum.m_dwObjSerial = -16;
    sPlayerDum.m_bLive = 1;
  }
}
