#include <windows.h>
#include <iostream>
#include <string>
#include <tlhelp32.h>

class DLLInjector {
public:
    static bool InjectDLL(const std::string& processName, const std::string& dllPath) {
        DWORD processId = GetProcessIdByName(processName);
        if (processId == 0) {
            std::cout << "Error: Process '" << processName << "' not found!" << std::endl;
            return false;
        }

        return InjectDLL(processId, dllPath);
    }

    static bool InjectDLL(DWORD processId, const std::string& dllPath) {
        // Open target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::cout << "Error: Failed to open process (PID: " << processId << ")" << std::endl;
            return false;
        }

        // Allocate memory in target process
        SIZE_T dllPathSize = dllPath.length() + 1;
        LPVOID pDllPath = VirtualAllocEx(hProcess, NULL, dllPathSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!pDllPath) {
            std::cout << "Error: Failed to allocate memory in target process" << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        // Write DLL path to target process memory
        if (!WriteProcessMemory(hProcess, pDllPath, dllPath.c_str(), dllPathSize, NULL)) {
            std::cout << "Error: Failed to write DLL path to target process" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        LPTHREAD_START_ROUTINE pLoadLibrary = (LPTHREAD_START_ROUTINE)GetProcAddress(hKernel32, "LoadLibraryA");
        if (!pLoadLibrary) {
            std::cout << "Error: Failed to get LoadLibraryA address" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Create remote thread to load DLL
        HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, pLoadLibrary, pDllPath, 0, NULL);
        if (!hThread) {
            std::cout << "Error: Failed to create remote thread" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Wait for thread completion
        WaitForSingleObject(hThread, INFINITE);

        // Get thread exit code (module handle)
        DWORD exitCode;
        GetExitCodeThread(hThread, &exitCode);

        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
        CloseHandle(hProcess);

        if (exitCode == 0) {
            std::cout << "Error: DLL injection failed (LoadLibrary returned NULL)" << std::endl;
            return false;
        }

        std::cout << "Success: DLL injected successfully (Module handle: 0x" << std::hex << exitCode << ")" << std::endl;
        return true;
    }

private:
    static DWORD GetProcessIdByName(const std::string& processName) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (!Process32First(hSnapshot, &pe32)) {
            CloseHandle(hSnapshot);
            return 0;
        }

        do {
            if (processName == pe32.szExeFile) {
                CloseHandle(hSnapshot);
                return pe32.th32ProcessID;
            }
        } while (Process32Next(hSnapshot, &pe32));

        CloseHandle(hSnapshot);
        return 0;
    }
};

void PrintUsage() {
    std::cout << "ZoneServerHook DLL Injector v1.0" << std::endl;
    std::cout << "Usage:" << std::endl;
    std::cout << "  Injector.exe <process_name> <dll_path>" << std::endl;
    std::cout << "  Injector.exe <process_id> <dll_path>" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  Injector.exe ZoneServer.exe ZoneServerHook.dll" << std::endl;
    std::cout << "  Injector.exe 1234 C:\\Path\\To\\ZoneServerHook.dll" << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc != 3) {
        PrintUsage();
        return 1;
    }

    std::string target = argv[1];
    std::string dllPath = argv[2];

    std::cout << "ZoneServerHook DLL Injector" << std::endl;
    std::cout << "Target: " << target << std::endl;
    std::cout << "DLL: " << dllPath << std::endl;
    std::cout << std::endl;

    // Check if target is a process ID (numeric) or process name
    bool isNumeric = true;
    for (char c : target) {
        if (!isdigit(c)) {
            isNumeric = false;
            break;
        }
    }

    bool success;
    if (isNumeric) {
        DWORD processId = std::stoul(target);
        success = DLLInjector::InjectDLL(processId, dllPath);
    } else {
        success = DLLInjector::InjectDLL(target, dllPath);
    }

    if (success) {
        std::cout << std::endl << "Injection completed successfully!" << std::endl;
        return 0;
    } else {
        std::cout << std::endl << "Injection failed!" << std::endl;
        return 1;
    }
}